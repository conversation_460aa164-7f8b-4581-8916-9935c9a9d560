<?php

namespace App\Models;

use App\Enums\WorkScheduleAssignmentType;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class WorkScheduleAssignment extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    protected $fillable = ['team_id', 'work_schedule_id', 'type', 'value'];

    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'work_schedule_id' => 'integer',
        'type' => WorkScheduleAssignmentType::class,
        'value' => 'collection',
    ];

    public function workSchedule(): BelongsTo
    {
        return $this->belongsTo(WorkSchedule::class);
    }
}
