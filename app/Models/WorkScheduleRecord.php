<?php

namespace App\Models;

use App\Enums\WorkdayType;
use App\Traits\BelongsToTenant;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class WorkScheduleRecord extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    protected $fillable = [
        'team_id',
        'work_schedule_id',
        'employee_id',
        'workday_id',
        'date',
        'workday_type',
    ];

    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'work_schedule_id' => 'integer',
        'employee_id' => 'integer',
        'workday_id' => 'integer',
        'date' => 'date',
        'workday_type' => WorkdayType::class,
    ];

    public function scopeDate(
        Builder $query,
        string|Carbon|CarbonImmutable|CarbonPeriod $date
    ): Builder {
        if ($date instanceof CarbonPeriod) {
            return $query->whereBetween('date', [$date->start, $date->end]);
        }

        return $query->where('date', Carbon::parse($date)->format('Y-m-d'));
    }

    public function workSchedule(): BelongsTo
    {
        return $this->belongsTo(WorkSchedule::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function workday(): BelongsTo
    {
        return $this->belongsTo(Workday::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }
}
