<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class WorkdayWorkSchedule extends Pivot
{
    use HasFactory;

    protected $fillable = [
        'workday_id',
        'work_schedule_id',
        'specific_days',
        'work_days_number',
        'off_days_number',
        'repetitions_number',
    ];

    protected $casts = [
        'id' => 'integer',
        'workday_id' => 'integer',
        'work_schedule_id' => 'integer',
        'specific_days' => 'array',
        'work_days_number' => 'integer',
        'off_days_number' => 'integer',
        'repetitions_number' => 'integer',
    ];
}
