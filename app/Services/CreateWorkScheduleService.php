<?php

namespace App\Services;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use DB;

class CreateWorkScheduleService
{
    public static function handle(array $data): WorkSchedule
    {
        return DB::transaction(function () use ($data) {
            // Extract workdays and assignments data
            $workdaysData = $data['workdays'];
            $assignmentsData = $data['assignments'] ?? null;
            unset($data['workdays'], $data['assignments']);

            $workSchedule = WorkSchedule::create($data);

            $workDaysPivotToSync = collect($workdaysData)->mapWithKeys(
                fn($workdayData) => [
                    $workdayData['workday_id'] => [
                        'specific_days' => $workdayData['specific_days'] ?? null,
                        'work_days_number' => $workdayData['work_days_number'] ?? null,
                        'off_days_number' => $workdayData['off_days_number'] ?? null,
                        'repetitions_number' => $workdayData['repetitions_number'] ?? null,
                    ],
                ]
            );

            $workSchedule->workdays()->sync($workDaysPivotToSync);

            // Handle assignments
            ManageWorkScheduleAssignmentsService::handle($workSchedule, $assignmentsData);

            // Dispatch job to generate records for assigned employees
            if ($assignmentsData) {
                GenerateWorkScheduleRecordsJob::dispatch($workSchedule);
            }

            return $workSchedule;
        });
    }
}
