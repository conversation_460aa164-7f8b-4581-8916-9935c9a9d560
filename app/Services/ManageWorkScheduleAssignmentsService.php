<?php

namespace App\Services;

use App\Enums\WorkScheduleAssignmentType;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleAssignment;

class ManageWorkScheduleAssignmentsService
{
    public static function handle(WorkSchedule $workSchedule, ?array $assignments): void
    {
        $workSchedule->assignments()->delete();

        if (empty($assignments)) {
            return;
        }

        foreach ($assignments as $type => $ids) {
            if (empty($ids)) {
                continue;
            }

            $assignmentType = self::mapAssignmentType($type);
            if (!$assignmentType) {
                continue;
            }

            WorkScheduleAssignment::create([
                'team_id' => $workSchedule->team_id,
                'work_schedule_id' => $workSchedule->id,
                'type' => $assignmentType,
                'value' => $ids,
            ]);
        }
    }

    private static function mapAssignmentType(string $type): ?WorkScheduleAssignmentType
    {
        return match ($type) {
            'employees' => WorkScheduleAssignmentType::Employee,
            'departments' => WorkScheduleAssignmentType::Department,
            'direct_managers' => WorkScheduleAssignmentType::DirectManager,
            'tags' => WorkScheduleAssignmentType::Tag,
            'locations' => WorkScheduleAssignmentType::Location,
            default => null,
        };
    }
}
