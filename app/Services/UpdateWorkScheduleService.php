<?php

namespace App\Services;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use DB;

class UpdateWorkScheduleService
{
    public static function handle(WorkSchedule $workSchedule, array $data): WorkSchedule
    {
        return DB::transaction(function () use ($workSchedule, $data) {
            // Extract workdays and assignments data
            $workdaysData = $data['workdays'];
            $assignmentsData = $data['assignments'] ?? null;
            unset($data['workdays'], $data['assignments']);

            // Update the work schedule
            $workSchedule->update($data);

            // Detach all existing workdays
            $workSchedule->workdays()->detach();

            // Attach new workdays with pivot data
            foreach ($workdaysData as $workdayData) {
                $workSchedule->workdays()->attach($workdayData['workday_id'], [
                    'specific_days' => $workdayData['specific_days'] ?? null,
                    'work_days_number' => $workdayData['work_days_number'] ?? null,
                    'off_days_number' => $workdayData['off_days_number'] ?? null,
                    'repetitions_number' => $workdayData['repetitions_number'] ?? null,
                ]);
            }

            // Handle assignments
            ManageWorkScheduleAssignmentsService::handle($workSchedule, $assignmentsData);

            // Dispatch job to generate new records for assigned employees
            if ($assignmentsData) {
                GenerateWorkScheduleRecordsJob::dispatch($workSchedule);
            }

            return $workSchedule;
        });
    }
}
