<?php

namespace App\Support;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Models\Workday;
use App\Models\WorkSchedule;
use Illuminate\Support\Collection;

class WorkScheduleRotationState
{
    private int $workdayIndex = 0;
    private int $workDaysCount = 0;
    private int $offDaysCount = 0;
    private int $repetitionsCount = 0;
    private bool $isInWorkPeriod = true;
    private int $offDaysAfterRepetitionCount = 0;
    private bool $isInOffDaysAfterRepetition = false;

    public function __construct(private readonly Collection $workdays)
    {
    }

    public function getCurrentWorkday(): ?Workday
    {
        if ($this->workdays->isEmpty()) {
            return null;
        }

        return $this->workdays[$this->workdayIndex];
    }

    public function advanceDay(WorkSchedule $workSchedule): void
    {
        // Handle off days after repetition first
        if ($this->isInOffDaysAfterRepetition) {
            $this->offDaysAfterRepetitionCount++;
            if (
                $this->offDaysAfterRepetitionCount >= $workSchedule->off_days_after_each_repetition
            ) {
                $this->isInOffDaysAfterRepetition = false;
                $this->offDaysAfterRepetitionCount = 0;
            }
            return;
        }

        // Handle rotation logic based on distribution type
        if (
            $workSchedule->work_and_off_days_distribution_type ===
            WorkAndOffDaysDistributionType::NumberOfDays
        ) {
            $this->advanceNumberOfDaysRotation($workSchedule);
        } else {
            $this->advanceSpecificDaysRotation($workSchedule);
        }
    }

    private function advanceNumberOfDaysRotation(WorkSchedule $workSchedule): void
    {
        $currentWorkday = $this->getCurrentWorkday();

        if ($this->isInWorkPeriod) {
            $this->workDaysCount++;
            if ($this->workDaysCount >= $currentWorkday->pivot->work_days_number) {
                $this->isInWorkPeriod = false;
                $this->workDaysCount = 0;
            }
            return;
        }

        $this->offDaysCount++;
        if ($this->offDaysCount >= $currentWorkday->pivot->off_days_number) {
            $this->isInWorkPeriod = true;
            $this->offDaysCount = 0;
            $this->repetitionsCount++;

            // Check if we need to move to next workday
            if ($this->repetitionsCount >= $currentWorkday->pivot->repetitions_number) {
                $this->moveToNextWorkday($workSchedule);
            }
        }
    }

    private function advanceSpecificDaysRotation(WorkSchedule $workSchedule): void
    {
        // For specific days distribution, we simply rotate through workdays
        // The weekend logic is handled in the job based on each workday's specific_days

        // For rotational schedules with specific days, we need to rotate through workdays
        // This is a simple rotation - each workday gets used for a period before moving to the next

        // For now, we'll rotate daily for specific days distribution
        // This could be enhanced to support more complex rotation patterns if needed
        if ($workSchedule->type === WorkScheduleType::Rotational) {
            // Simple daily rotation for rotational schedules with specific days
            $this->workdayIndex = ($this->workdayIndex + 1) % $this->workdays->count();
        }
        // For fixed schedules with specific days, we don't rotate (stay on the same workday)
    }

    private function moveToNextWorkday(WorkSchedule $workSchedule): void
    {
        $this->repetitionsCount = 0;
        $this->workdayIndex = ($this->workdayIndex + 1) % $this->workdays->count();

        // Start off days after repetition if configured
        if ($workSchedule->off_days_after_each_repetition > 0) {
            $this->isInOffDaysAfterRepetition = true;
            $this->offDaysAfterRepetitionCount = 0;
        }
    }
}
