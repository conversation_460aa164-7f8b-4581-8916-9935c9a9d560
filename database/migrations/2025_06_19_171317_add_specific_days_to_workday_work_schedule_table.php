<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workday_work_schedule', function (Blueprint $table) {
            if (!Schema::hasColumn('workday_work_schedule', 'specific_days')) {
                $table->json('specific_days')->nullable()->after('work_schedule_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workday_work_schedule', function (Blueprint $table) {
            $table->dropColumn('specific_days');
        });
    }
};
