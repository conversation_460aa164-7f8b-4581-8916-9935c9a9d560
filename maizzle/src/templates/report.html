---
title: "تقرير"
bodyClass: bg-gray-50
---

@php
  /** @var \App\Models\Employee $employee */
  /** @var \App\Models\ReportTask $reportTask */
@endphp
<x-main>
  <div class="bg-slate-50 sm:px-4 font-sans p-8 shadow-sm">
    <table align="center">
      <tr>
        <td class="max-w-full bg-white shadow">
          <div class="mt-12 mb-6 sm:mt-8 sm:mb-4 text-center">
            <img
              class="block mx-auto h-11 w-auto"
              src="{{asset('images/logo.png')}}"
              alt="Nawart"
            />
          </div>

          <table class="w-full">
            <tr>
              <td class="p-12 sm:px-6 text-base text-slate-700 bg-white rounded shadow-sm">
                <h1
                  class="m-0 mb-6 text-3xl sm:leading-8 text-gray-900 font-semibold text-center">{{$reportTask->report->name->displayName()}}</h1>

                <p class="font-normal text-gray-600">
                  {!!  __('Welcome <strong>:name</strong>', ['name' => $employee->name])!!},
                </p>

                <p class="font-normal text-gray-600">
                  @if($reportTask->data?->period)
                    {!!__('The report <strong>:report</strong> for the period <strong>(:from, :to)</strong>, is here for you to review!', [
                    'report' => $reportTask->report->name->displayName(),
                    'from' => $reportTask->data->period->start->format('Y-m-d'),
                    'to' => $reportTask->data->period->end->format('Y-m-d'),
                  ])!!}
                  @endif
                  <br>
                  {{__('Please find the details in the attached file.')}}
                </p>

              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</x-main>
