<?php

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleAssignment;
use Illuminate\Support\Facades\Queue;
use function Pest\Laravel\artisan;

beforeEach(function () {
    Queue::fake();

    $this->department = Department::factory()
        ->for($this->tenant)
        ->create();

    $this->workday = Workday::factory()
        ->for($this->tenant)
        ->create();
});

test('dispatches jobs for all active work schedules with assignments', function () {
    // Create active work schedules with assignments
    $workSchedule1 = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule1->workdays()->attach($this->workday->id);

    $workSchedule2 = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->numberOfDays()
        ->create();

    $workSchedule2->workdays()->attach($this->workday->id);

    // Create employees
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    // Create assignments for both work schedules
    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workSchedule1->id,
            'value' => [$employee1->id],
        ]);

    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workSchedule2->id,
            'value' => [$employee2->id],
        ]);

    // Run the command
    artisan('work-schedule:generate-records')->assertSuccessful();

    // Assert that jobs were dispatched for both work schedules
    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class, 2);
});

test('does not dispatch jobs for work schedules without assignments', function () {
    // Create work schedule without assignments
    $workScheduleWithoutAssignments = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workScheduleWithoutAssignments->workdays()->attach($this->workday->id);

    // Create work schedule with assignments
    $workScheduleWithAssignments = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workScheduleWithAssignments->workdays()->attach($this->workday->id);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workScheduleWithAssignments->id,
            'value' => [$employee->id],
        ]);

    // Run the command
    artisan('work-schedule:generate-records')->assertSuccessful();

    // Should only dispatch job for work schedule with assignments
    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class, 1);
});

test('does not dispatch jobs for work schedules belonging to inactive teams', function () {
    // Create inactive team
    $inactiveTeam = Team::factory()->inactive()->create();

    // Create work schedule for inactive team
    $workScheduleInactiveTeam = WorkSchedule::factory()
        ->for($inactiveTeam)
        ->fixed()
        ->specificDays()
        ->create();

    $workScheduleInactiveTeam->workdays()->attach($this->workday->id);

    $departmentInactive = Department::factory()->for($inactiveTeam)->create();

    $employeeInactive = Employee::factory()
        ->for($inactiveTeam)
        ->for($departmentInactive)
        ->active()
        ->create();

    WorkScheduleAssignment::factory()
        ->for($inactiveTeam)
        ->employee()
        ->create([
            'work_schedule_id' => $workScheduleInactiveTeam->id,
            'value' => [$employeeInactive->id],
        ]);

    // Create work schedule for active team
    $workScheduleActiveTeam = WorkSchedule::factory()
        ->for($this->tenant) // Active team
        ->fixed()
        ->specificDays()
        ->create();

    $workScheduleActiveTeam->workdays()->attach($this->workday->id);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workScheduleActiveTeam->id,
            'value' => [$employee->id],
        ]);

    // Run the command
    artisan('work-schedule:generate-records')->assertSuccessful();

    // Should only dispatch job for work schedule belonging to active team
    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class, 1);
});

test('handles empty database gracefully', function () {
    // Ensure no work schedules exist
    WorkSchedule::query()->delete();

    // Run the command
    artisan('work-schedule:generate-records')->assertSuccessful();

    // Should not dispatch any jobs
    Queue::assertNotPushed(GenerateWorkScheduleRecordsJob::class);
});

test('command has correct signature and description', function () {
    // Test that the command can be found and has the correct signature
    artisan('work-schedule:generate-records --help')->assertSuccessful();

    // The command should exist and be callable
    expect(true)->toBeTrue(); // This test passes if the command runs without error
});

test('processes large number of work schedules efficiently', function () {
    // Create multiple work schedules with assignments
    $workSchedules = [];
    $employees = [];

    for ($i = 0; $i < 10; $i++) {
        $workSchedule = WorkSchedule::factory()
            ->for($this->tenant)
            ->fixed()
            ->specificDays()
            ->create();

        $workSchedule->workdays()->attach($this->workday->id);

        $employee = Employee::factory()
            ->for($this->tenant)
            ->for($this->department)
            ->active()
            ->create();

        WorkScheduleAssignment::factory()
            ->for($this->tenant)
            ->employee()
            ->create([
                'work_schedule_id' => $workSchedule->id,
                'value' => [$employee->id],
            ]);

        $workSchedules[] = $workSchedule;
        $employees[] = $employee;
    }

    // Run the command
    artisan('work-schedule:generate-records')->assertSuccessful();

    // Should dispatch jobs for all work schedules
    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class, 10);
});

test('command returns success exit code', function () {
    // Create a work schedule with assignment
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule->workdays()->attach($this->workday->id);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workSchedule->id,
            'value' => [$employee->id],
        ]);

    // Run the command and check exit code
    artisan('work-schedule:generate-records')->assertExitCode(0); // SUCCESS
});
