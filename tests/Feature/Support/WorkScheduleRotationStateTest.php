<?php

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Support\WorkScheduleRotationState;

beforeEach(function () {
    $this->tenant = Team::factory()->create();
    
    $this->workday1 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Day Shift']);
    
    $this->workday2 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Night Shift']);
    
    $this->workday3 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Weekend Shift']);
});

// Tests for advanceSpecificDaysRotation method
test('advanceSpecificDaysRotation rotates through workdays for rotational schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // First advance - should move to second workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    // Second advance - should move to third workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday3);
    
    // Third advance - should wrap around to first workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Fourth advance - should move to second workday again
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation does not rotate for fixed schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Multiple advances should stay on the same workday for fixed schedule
    for ($i = 0; $i < 10; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles single workday correctly', function () {
    $workdays = collect([$this->workday1]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be the only workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Multiple advances should stay on the same workday (no other workdays to rotate to)
    for ($i = 0; $i < 5; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles two workdays correctly', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Test alternating pattern
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation maintains state across multiple calls', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Track the rotation pattern over multiple cycles
    $expectedPattern = [$this->workday1, $this->workday2, $this->workday3];
    
    for ($cycle = 0; $cycle < 3; $cycle++) {
        for ($day = 0; $day < 3; $day++) {
            if ($cycle === 0 && $day === 0) {
                // Initial state
                expect($rotationState->getCurrentWorkday())->toBe($expectedPattern[$day]);
            } else {
                $rotationState->advanceDay($workSchedule);
                expect($rotationState->getCurrentWorkday())->toBe($expectedPattern[$day]);
            }
        }
    }
});

test('advanceSpecificDaysRotation is not affected by off_days_after_each_repetition', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make([
            'off_days_after_each_repetition' => 2, // This should not affect specific days rotation
        ]);
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Should still rotate normally regardless of off_days_after_each_repetition
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
});

test('getCurrentWorkday returns correct workday at any point in rotation', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Test that getCurrentWorkday always returns a valid workday
    for ($i = 0; $i < 20; $i++) {
        $currentWorkday = $rotationState->getCurrentWorkday();
        expect($currentWorkday)->toBeInstanceOf(Workday::class);
        expect($workdays->contains($currentWorkday))->toBeTrue();
        
        $rotationState->advanceDay($workSchedule);
    }
});

test('rotation state handles empty workdays collection gracefully', function () {
    $workdays = collect([]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    // This should not crash, but getCurrentWorkday will return null
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // We expect this to return null since there are no workdays
    expect($rotationState->getCurrentWorkday())->toBeNull();
});

test('rotation state maintains consistency across different schedule types', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    // Test Fixed + SpecificDays
    $fixedSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();
    
    $rotationState1 = new WorkScheduleRotationState($workdays);
    
    for ($i = 0; $i < 5; $i++) {
        expect($rotationState1->getCurrentWorkday())->toBe($this->workday1);
        $rotationState1->advanceDay($fixedSpecificSchedule);
    }
    
    // Test Rotational + SpecificDays
    $rotationalSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState2 = new WorkScheduleRotationState($workdays);
    
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday2);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
});

// Additional tests for advanced rotation scenarios
test('advanceSpecificDaysRotation works correctly with many workdays', function () {
    // Create 5 workdays to test larger rotation cycles
    $workdays = collect([
        $this->workday1,
        $this->workday2,
        $this->workday3,
        Workday::factory()->for($this->tenant)->create(['name' => 'Shift 4']),
        Workday::factory()->for($this->tenant)->create(['name' => 'Shift 5']),
    ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Test full rotation cycle
    for ($i = 0; $i < 10; $i++) { // Two full cycles
        $expectedWorkday = $workdays[$i % 5];
        expect($rotationState->getCurrentWorkday())->toBe($expectedWorkday);
        $rotationState->advanceDay($workSchedule);
    }
});

test('advanceSpecificDaysRotation handles workday index bounds correctly', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Advance many times to test index wrapping
    for ($i = 0; $i < 100; $i++) {
        $currentWorkday = $rotationState->getCurrentWorkday();
        expect($workdays->contains($currentWorkday))->toBeTrue();
        $rotationState->advanceDay($workSchedule);
    }
});

test('advanceSpecificDaysRotation performance with large workday collections', function () {
    // Create a large collection of workdays
    $workdays = collect();
    for ($i = 0; $i < 50; $i++) {
        $workdays->push(Workday::factory()->for($this->tenant)->create(['name' => "Shift $i"]));
    }

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Test that rotation works efficiently with large collections
    $startTime = microtime(true);

    for ($i = 0; $i < 1000; $i++) {
        $rotationState->advanceDay($workSchedule);
    }

    $endTime = microtime(true);
    $executionTime = $endTime - $startTime;

    // Should complete in reasonable time (less than 1 second)
    expect($executionTime)->toBeLessThan(1.0);
});
