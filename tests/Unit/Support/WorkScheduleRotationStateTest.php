<?php

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Support\WorkScheduleRotationState;
use Illuminate\Support\Collection;

beforeEach(function () {
    // Create simple mock workday objects instead of using Eloquent models
    $this->workday1 = (object) [
        'id' => 1,
        'name' => 'Day Shift',
    ];

    $this->workday2 = (object) [
        'id' => 2,
        'name' => 'Night Shift',
    ];

    $this->workday3 = (object) [
        'id' => 3,
        'name' => 'Weekend Shift',
    ];
});

// Tests for advanceSpecificDaysRotation method
test('advanceSpecificDaysRotation rotates through workdays for rotational schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // First advance - should move to second workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    // Second advance - should move to third workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday3);
    
    // Third advance - should wrap around to first workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Fourth advance - should move to second workday again
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation does not rotate for fixed schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Multiple advances should stay on the same workday for fixed schedule
    for ($i = 0; $i < 10; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles single workday correctly', function () {
    $workdays = collect([$this->workday1]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Initial state should be the only workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    // Multiple advances should stay on the same workday (no other workdays to rotate to)
    for ($i = 0; $i < 5; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles two workdays correctly', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Test alternating pattern
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation maintains state across multiple calls', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Track the rotation pattern over multiple cycles
    $expectedPattern = [$this->workday1, $this->workday2, $this->workday3];
    
    for ($cycle = 0; $cycle < 3; $cycle++) {
        for ($day = 0; $day < 3; $day++) {
            if ($cycle === 0 && $day === 0) {
                // Initial state
                expect($rotationState->getCurrentWorkday())->toBe($expectedPattern[$day]);
            } else {
                $rotationState->advanceDay($workSchedule);
                expect($rotationState->getCurrentWorkday())->toBe($expectedPattern[$day]);
            }
        }
    }
});

// Tests for integration with off_days_after_each_repetition
test('advanceSpecificDaysRotation is not affected by off_days_after_each_repetition', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make([
            'off_days_after_each_repetition' => 2, // This should not affect specific days rotation
        ]);
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Should still rotate normally regardless of off_days_after_each_repetition
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
    
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
});

// Tests for edge cases
test('getCurrentWorkday returns correct workday at any point in rotation', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);
    
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState = new WorkScheduleRotationState($workdays);
    
    // Test that getCurrentWorkday always returns a valid workday
    for ($i = 0; $i < 20; $i++) {
        $currentWorkday = $rotationState->getCurrentWorkday();
        expect($currentWorkday)->toBeInstanceOf(Workday::class);
        expect($workdays->contains($currentWorkday))->toBeTrue();
        
        $rotationState->advanceDay($workSchedule);
    }
});

test('advanceSpecificDaysRotation works with different distribution types', function () {
    $workdays = collect([$this->workday1, $this->workday2]);
    
    // Test with SpecificDays distribution type
    $specificDaysSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();
    
    $rotationState1 = new WorkScheduleRotationState($workdays);
    
    expect($rotationState1->getCurrentWorkday())->toBe($this->workday1);
    $rotationState1->advanceDay($specificDaysSchedule);
    expect($rotationState1->getCurrentWorkday())->toBe($this->workday2);
    
    // Test with NumberOfDays distribution type (should use different logic)
    $numberOfDaysSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->numberOfDays()
        ->make();
    
    $rotationState2 = new WorkScheduleRotationState($workdays);
    
    // For NumberOfDays, the rotation logic is different and handled by advanceNumberOfDaysRotation
    // This test ensures that the correct method is called based on distribution type
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
    // The behavior will be different for NumberOfDays, but we're just testing the method dispatch
});

// Tests for NumberOfDays rotation (for comparison and completeness)
test('advanceNumberOfDaysRotation follows work/off day patterns', function () {
    $workdays = collect([
        (object) [
            'id' => $this->workday1->id,
            'pivot' => (object) [
                'work_days_number' => 3,
                'off_days_number' => 2,
                'repetitions_number' => 2,
            ]
        ]
    ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->numberOfDays()
        ->make([
            'off_days_after_each_repetition' => 0,
        ]);

    $rotationState = new WorkScheduleRotationState($workdays);

    // Should stay on same workday for the work period
    expect($rotationState->getCurrentWorkday())->toBe($workdays[0]);

    // Advance through work days (3 days)
    for ($i = 0; $i < 3; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($workdays[0]);
    }

    // Advance through off days (2 days) - should still be same workday
    for ($i = 0; $i < 2; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($workdays[0]);
    }
});

test('rotation state maintains consistency across different schedule types', function () {
    $workdays = collect([$this->workday1, $this->workday2]);

    // Test Fixed + SpecificDays
    $fixedSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();

    $rotationState1 = new WorkScheduleRotationState($workdays);

    for ($i = 0; $i < 5; $i++) {
        expect($rotationState1->getCurrentWorkday())->toBe($this->workday1);
        $rotationState1->advanceDay($fixedSpecificSchedule);
    }

    // Test Rotational + SpecificDays
    $rotationalSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState2 = new WorkScheduleRotationState($workdays);

    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday2);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
});

test('rotation state handles empty workdays collection gracefully', function () {
    $workdays = collect([]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    // This should not crash, but getCurrentWorkday will return null
    $rotationState = new WorkScheduleRotationState($workdays);

    // We expect this to return null since there are no workdays
    expect($rotationState->getCurrentWorkday())->toBeNull();
});
